<?php

namespace App\Models;

use CodeIgniter\Model;

class WorkplanInfrastructureActivitiesModel extends Model
{
    protected $table            = 'workplan_infrastructure_activities';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = true;

    protected $allowedFields = [
        'workplan_id',
        'infrastructure_name',
        'description',
        'province_id',
        'district_id',
        'location',
        'gps_coordinates',
        'supervisor_id',
        'action_officer_id',
        'infrastructure_images',
        'infrastructure_status',
        'infrastructure_status_by',
        'infrastructure_status_at',
        'infrastructure_status_remarks',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    protected $validationRules = [
        'workplan_id'         => 'required|integer',
        'infrastructure_name' => 'required|min_length[3]|max_length[255]',
        'province_id'         => 'required|integer',
        'district_id'         => 'required|integer',
        'location'            => 'required|min_length[3]|max_length[255]',
        'infrastructure_status' => 'permit_empty|in_list[pending,submitted,approved,rated]'
    ];

    protected $validationMessages = [
        'workplan_id' => [
            'required' => 'Workplan ID is required',
            'integer'  => 'Workplan ID must be a valid integer'
        ],
        'infrastructure_name' => [
            'required'   => 'Infrastructure name is required',
            'min_length' => 'Infrastructure name must be at least 3 characters long',
            'max_length' => 'Infrastructure name cannot exceed 255 characters'
        ],
        'province_id' => [
            'required' => 'Province is required',
            'integer'  => 'Province ID must be a valid integer'
        ],
        'district_id' => [
            'required' => 'District is required',
            'integer'  => 'District ID must be a valid integer'
        ],
        'location' => [
            'required'   => 'Location is required',
            'min_length' => 'Location must be at least 3 characters long',
            'max_length' => 'Location cannot exceed 255 characters'
        ],
        'infrastructure_status' => [
            'in_list' => 'Infrastructure status must be pending, submitted, approved, or rated'
        ]
    ];

    protected $skipValidation     = false;
    protected $cleanValidationRules = true;

    /**
     * Get activities by status
     *
     * @param string $status
     * @return array
     */
    public function getByStatus($status)
    {
        return $this->where('infrastructure_status', $status)
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get activities by workplan
     *
     * @param int $workplanId
     * @return array
     */
    public function getByWorkplan($workplanId)
    {
        return $this->where('workplan_id', $workplanId)
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get activities with location details
     *
     * @return array
     */
    public function getActivitiesWithLocationDetails()
    {
        return $this->select('workplan_infrastructure_activities.*, 
                             adx_province.name as province_name,
                             adx_district.name as district_name,
                             supervisor.name as supervisor_name,
                             officer.name as action_officer_name')
                    ->join('adx_province', 'adx_province.id = workplan_infrastructure_activities.province_id', 'left')
                    ->join('adx_district', 'adx_district.id = workplan_infrastructure_activities.district_id', 'left')
                    ->join('users as supervisor', 'supervisor.id = workplan_infrastructure_activities.supervisor_id', 'left')
                    ->join('users as officer', 'officer.id = workplan_infrastructure_activities.action_officer_id', 'left')
                    ->orderBy('workplan_infrastructure_activities.created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Update infrastructure status
     *
     * @param int $id
     * @param string $status
     * @param int $statusBy
     * @param string $remarks
     * @return bool
     */
    public function updateStatus($id, $status, $statusBy, $remarks = null)
    {
        $data = [
            'infrastructure_status' => $status,
            'infrastructure_status_by' => $statusBy,
            'infrastructure_status_at' => date('Y-m-d H:i:s'),
            'infrastructure_status_remarks' => $remarks
        ];

        return $this->update($id, $data);
    }
}
