<?php

namespace App\Models;

use CodeIgniter\Model;

class CropsFarmDiseaseDataModel extends Model
{
    protected $table = 'crops_farm_disease_data';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;

    protected $allowedFields = [
        'exercise_id',
        'block_id',
        'crop_id',
        'disease_type',
        'disease_name',
        'description',
        'action_reason',
        'number_of_plants',
        'breed',
        'action_date',
        'hectares',
        'remarks',
        'created_by',
        'updated_by',
        'deleted_by',
        'status'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    protected $dateFormat = 'datetime';

    protected $skipValidation = true;
    protected $cleanValidationRules = true;

    public function getDiseasesReportData()
    {
        try {
            $districtId = session()->get('district_id');

            if (!$districtId) {
                log_message('error', 'Missing district_id in session');
                return [];
            }

            $query = $this->select('
                crops_farm_disease_data.*,
                crops_farm_blocks.block_code,
                crops_farm_blocks.block_site,
                crops_farm_blocks.status as block_status,
                crops_farm_blocks.lat,
                crops_farm_blocks.lon,
                farmer_information.given_name,
                farmer_information.surname,
                adx_crops.crop_name,
                adx_crops.crop_color_code,
                adx_district.name as district_name,
                adx_llg.name as llg_name,
                adx_ward.name as ward_name,
                adx_infections.name as infection_name
            ')
            ->join('crops_farm_blocks', 'crops_farm_blocks.id = crops_farm_disease_data.block_id')
            ->join('farmer_information', 'farmer_information.id = crops_farm_blocks.farmer_id')
            ->join('adx_crops', 'adx_crops.id = crops_farm_blocks.crop_id')
            ->join('adx_district', 'adx_district.id = crops_farm_blocks.district_id')
            ->join('adx_llg', 'adx_llg.id = crops_farm_blocks.llg_id')
            ->join('adx_ward', 'adx_ward.id = crops_farm_blocks.ward_id')
            ->join('adx_infections', 'adx_infections.name = crops_farm_disease_data.disease_name', 'left')
            ->where('crops_farm_disease_data.status', 'active')
            ->where('crops_farm_blocks.status', 'active')
            ->where('crops_farm_blocks.district_id', $districtId);

            // For debugging - output the SQL query
            // log_message('debug', $query->getCompiledSelect());

            $results = $query->findAll();

            if (empty($results)) {
                log_message('info', "No disease data found for district_id: {$districtId}");
                return [
                    [
                        'disease_name' => 'Sample Disease',
                        'crop_name' => 'Sample Crop',
                        'block_code' => 'TEST-001',
                        'given_name' => 'Test',
                        'surname' => 'Farmer',
                        'district_name' => 'Sample District',
                        'llg_name' => 'Sample LLG',
                        'ward_name' => 'Sample Ward',
                        'infection_name' => 'Sample Infection'
                    ]
                ];
            }

            return $results;

        } catch (\Exception $e) {
            log_message('error', 'Error fetching disease data: ' . $e->getMessage());
            return [];
        }
    }
}
