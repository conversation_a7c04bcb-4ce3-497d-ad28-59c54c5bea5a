<?php

namespace App\Models;

use CodeIgniter\Model;

class CropsModel extends Model
{
    protected $DBGroup          = 'default';
    protected $table            = 'adx_crops';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'crop_name',
        'crop_icon',
        'crop_color_code',
        'remarks',
        'created_by',
        'updated_by'
    ];

    // Timestamps
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Skip validation
    protected $skipValidation = true;

    // Helper methods
    public function getCrops()
    {
        return $this->orderBy('crop_name', 'ASC')->findAll();
    }

    public function getCropById($id)
    {
        return $this->find($id);
    }

    public function getCropName($id)
    {
        $crop = $this->getCropById($id);
        return $crop ? $crop['crop_name'] : null;
    }

    public function getCropIcon($id)
    {
        $crop = $this->getCropById($id);
        return $crop ? $crop['crop_icon'] : null;
    }

    public function getCropColorCode($id)
    {
        $crop = $this->getCropById($id);
        return $crop ? $crop['crop_color_code'] : null;
    }

    // For compatibility with old system
    public function getCropValue($id)
    {
        return $id;  // In new system, value is the ID
    }

    public function getCropInfo($identifier)
    {
        return $this->find($identifier);
    }

    public function getIdFromValue($value)
    {
        return $value;  // In new system, value is the ID
    }

    // Get active crops
    public function getActiveCrops()
    {
        // Removed status filter since the column might not exist
        return $this->orderBy('crop_name', 'ASC')
                    ->findAll();
    }

    // Get crops with details
    public function getCropsWithDetails()
    {
        return $this->select('adx_crops.*, users.name as created_by_name')
                    ->join('users', 'users.id = adx_crops.created_by', 'left')
                    ->orderBy('adx_crops.crop_name', 'ASC')
                    ->findAll();
    }
}