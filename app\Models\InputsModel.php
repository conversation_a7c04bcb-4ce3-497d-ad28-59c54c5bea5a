<?php

namespace App\Models;

use CodeIgniter\Model;

class InputsModel extends Model
{
    protected $table            = 'inputs';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = true;
    protected $protectFields    = true;
    
    protected $allowedFields = [
        'country_id',
        'province_id',
        'district_id',
        'llg_id',
        'locations',
        'gps',
        'officers',
        'item',
        'description',
        'unit',
        'quantity',
        'remarks',
        'recipients',
        'status',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';

    // Cast fields
    protected $casts = [
        'quantity'   => 'decimal'
    ];

    // Validation
    protected $validationRules = [
        'country_id'  => 'required|numeric',
        'province_id' => 'required|numeric',
        'district_id' => 'required|numeric',
        'llg_id'      => 'required|numeric',
        'locations'   => 'required',
        'gps'         => 'required',
        'item'        => 'required|min_length[2]|max_length[255]',
        'unit'        => 'required|max_length[50]',
        'quantity'    => 'required|numeric',
        'status'      => 'required|numeric'
    ];
    
    protected $validationMessages = [];
    protected $skipValidation     = false;
    protected $cleanValidationRules = true;
}
