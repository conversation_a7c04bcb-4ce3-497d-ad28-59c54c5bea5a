<?php

namespace App\Models;

use CodeIgniter\Model;

class FertilizersModel extends Model
{
    protected $table = 'adx_fertilizers';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $allowedFields = [
        'name',
        'icon',
        'color_code',
        'remarks',
        'created_by',
        'updated_by'
    ];

    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $skipValidation = true;
} 