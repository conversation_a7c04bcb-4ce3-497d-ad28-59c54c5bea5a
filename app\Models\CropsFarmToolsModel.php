<?php

namespace App\Models;

use CodeIgniter\Model;

class CropsFarmToolsModel extends Model
{
    protected $table            = 'crops_farm_tools';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;

    protected $allowedFields = [
        'tool_type',
        'crop_id',
        'tool_name',
        'remarks',
        'status'
    ];

    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    protected $validationRules = [
        'tool_type' => 'required|in_list[general,speciality]',
        'tool_name' => 'required|min_length[2]|max_length[255]',
        'status'    => 'permit_empty|in_list[active,inactive]'
    ];

    protected $validationMessages = [
        'tool_type' => [
            'required' => 'Tool type is required',
            'in_list'  => 'Tool type must be either general or speciality'
        ],
        'tool_name' => [
            'required'   => 'Tool name is required',
            'min_length' => 'Tool name must be at least 2 characters long',
            'max_length' => 'Tool name cannot exceed 255 characters'
        ],
        'status' => [
            'in_list' => 'Status must be either active or inactive'
        ]
    ];

    protected $skipValidation     = false;
    protected $cleanValidationRules = true;

    /**
     * Get tools by type
     *
     * @param string $type
     * @return array
     */
    public function getByType($type)
    {
        return $this->where('tool_type', $type)
                    ->where('status', 'active')
                    ->orderBy('tool_name', 'ASC')
                    ->findAll();
    }

    /**
     * Get tools by crop
     *
     * @param int $cropId
     * @return array
     */
    public function getByCrop($cropId)
    {
        return $this->where('crop_id', $cropId)
                    ->where('status', 'active')
                    ->orderBy('tool_name', 'ASC')
                    ->findAll();
    }

    /**
     * Get general tools
     *
     * @return array
     */
    public function getGeneralTools()
    {
        return $this->getByType('general');
    }

    /**
     * Get specialty tools
     *
     * @return array
     */
    public function getSpecialtyTools()
    {
        return $this->getByType('speciality');
    }

    /**
     * Get tools with crop information
     *
     * @return array
     */
    public function getToolsWithCropInfo()
    {
        return $this->select('crops_farm_tools.*, adx_crops.crop_name')
                    ->join('adx_crops', 'adx_crops.id = crops_farm_tools.crop_id', 'left')
                    ->where('crops_farm_tools.status', 'active')
                    ->orderBy('crops_farm_tools.tool_type', 'ASC')
                    ->orderBy('crops_farm_tools.tool_name', 'ASC')
                    ->findAll();
    }
}
