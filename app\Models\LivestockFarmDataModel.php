<?php

namespace App\Models;

use CodeIgniter\Model;

class LivestockFarmDataModel extends Model
{
    protected $table = 'livestock_farm_data';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;
    protected $allowedFields = [
        'exercise_id',
        'block_id',
        'livestock_id',
        'breed',
        'he_total',
        'she_total',
        'pasture_type',
        'growth_stage',
        'comments',
        'action_date',
        'cost_per_livestock',
        'low_price_per_livestock',
        'high_price_per_livestock',
        'created_by',
        'updated_by',
        'deleted_by',
        'status'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';

    // Validation
    protected $validationRules = [
        'block_id' => 'required|numeric',
        'livestock_id' => 'required|numeric',
        'he_total' => 'permit_empty|numeric',
        'she_total' => 'permit_empty|numeric',
        'cost_per_livestock' => 'permit_empty|numeric',
        'low_price_per_livestock' => 'permit_empty|numeric',
        'high_price_per_livestock' => 'permit_empty|numeric',
        'status' => 'required|in_list[active,inactive,deleted]'
    ];

    protected $validationMessages = [];
    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Get livestock data with block and livestock details
    public function getLivestockDataWithDetails($conditions = [])
    {
        $builder = $this->builder();
        $builder->select('
            livestock_farm_data.*,
            livestock_farm_blocks.block_code,
            livestock_farm_blocks.block_site,
            adx_livestock.livestock_name,
            adx_livestock.livestock_color_code
        ')
        ->join('livestock_farm_blocks', 'livestock_farm_blocks.id = livestock_farm_data.block_id')
        ->join('adx_livestock', 'adx_livestock.id = livestock_farm_data.livestock_id')
        ->where('livestock_farm_data.status', 'active')
        ->where('livestock_farm_blocks.status', 'active');

        // Apply conditions if any
        if (!empty($conditions)) {
            $builder->where($conditions);
        }

        return $builder->get()->getResultArray();
    }

    // Get livestock summary
    public function getLivestockSummary($conditions = [])
    {
        $builder = $this->builder();
        $builder->select('
            livestock_farm_data.livestock_id,
            adx_livestock.livestock_name,
            adx_livestock.livestock_color_code,
            SUM(livestock_farm_data.he_total) as total_male,
            SUM(livestock_farm_data.she_total) as total_female,
            COUNT(DISTINCT livestock_farm_data.block_id) as total_blocks,
            AVG(livestock_farm_data.cost_per_livestock) as avg_cost,
            AVG((livestock_farm_data.low_price_per_livestock + livestock_farm_data.high_price_per_livestock) / 2) as avg_price
        ')
        ->join('livestock_farm_blocks', 'livestock_farm_blocks.id = livestock_farm_data.block_id')
        ->join('adx_livestock', 'adx_livestock.id = livestock_farm_data.livestock_id')
        ->where('livestock_farm_data.status', 'active')
        ->where('livestock_farm_blocks.status', 'active');

        // Apply conditions if any
        if (!empty($conditions)) {
            $builder->where($conditions);
        }

        return $builder->groupBy('livestock_farm_data.livestock_id')
            ->get()
            ->getResultArray();
    }
}
