<?php

namespace App\Models;

use CodeIgniter\Model;

class CropsFarmBlockModel extends Model
{
    protected $table = 'crops_farm_blocks';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = true;

    protected $allowedFields = [
        'exercise_id',
        'farmer_id',
        'crop_id',
        'block_code',
        'org_id',
        'country_id',
        'province_id',
        'district_id',
        'llg_id',
        'ward_id',
        'village',
        'block_site',
        'lon',
        'lat',
        'remarks',
        'created_by',
        'updated_by',
        'deleted_by',
        'status'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';
    protected $deletedField = 'deleted_at';
    protected $dateFormat = 'datetime';

    protected $skipValidation = true;
    protected $cleanValidationRules = true;

    public function getFarmBlocksWithDetails($district_id = null)
    {
        $builder = $this->db->table($this->table . ' as fb');
        $builder->select('fb.*, f.given_name, f.surname, f.gender, c.crop_name, c.crop_color_code, d.name as district_name, l.name as llg_name, w.name as ward_name')
                ->join('farmer_information as f', 'f.id = fb.farmer_id', 'left')
                ->join('adx_crops as c', 'c.id = fb.crop_id', 'left')
                ->join('adx_district as d', 'd.id = fb.district_id', 'left')
                ->join('adx_llg as l', 'l.id = fb.llg_id', 'left')
                ->join('adx_ward as w', 'w.id = fb.ward_id', 'left')
                ->where('fb.status !=', 'deleted');

        if ($district_id) {
            $builder->where('fb.district_id', $district_id);
        }

        $query = $builder->get();
        $results = $query->getResultArray();

        // Keep individual name fields for the view
        foreach ($results as &$result) {
            $result['farmer_name'] = $result['given_name'] . ' ' . $result['surname'];
        }

        return $results;
    }
}
